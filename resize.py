import os
from PIL import Image

def resize_images_in_folder(root_folder):
    # 使用os.walk递归遍历所有文件夹和文件
    for dirpath, dirnames, filenames in os.walk(root_folder):
        for filename in filenames:
            file_path = os.path.join(dirpath, filename)
            try:
                # 判断文件是否为图像格式（这里可以根据实际情况扩展支持的格式）
                if filename.lower().endswith(('jpg', 'jpeg', 'png', 'bmp', 'tiff')):
                    with Image.open(file_path) as img:
                        # 如果图像尺寸是240x240，进行resize
                        if img.size == (240, 240):
                            img_resized = img.resize((256, 256))
                            # 保存为原文件名
                            img_resized.save(file_path)
                            print(f"Resized: {file_path}")
            except Exception as e:
                print(f"Error processing file {file_path}: {e}")

# 设置文件夹路径
folder_path = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset1\train'  # 请将此路径替换为你想遍历的文件夹路径

resize_images_in_folder(folder_path)
