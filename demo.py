import os
import cv2

def crop_and_save_images(input_root, output_root):
    for root, dirs, files in os.walk(input_root):
        for file in files:
            if file.endswith(".png") and "_" in file:
                input_path = os.path.join(root, file)
                rel_path = os.path.relpath(root, input_root)

                # 读取图像
                img = cv2.imread(input_path)
                if img is None or img.shape[1] != 858 or img.shape[0] != 286:
                    print(f"跳过尺寸不符的图像：{input_path}")
                    continue

                # 切割图像（286x286）
                left = img[:, 0:286]
                middle = img[:, 286:572]

                # 缩放到 256x256
                left_resized = cv2.resize(left, (256, 256), interpolation=cv2.INTER_AREA)
                middle_resized = cv2.resize(middle, (256, 256), interpolation=cv2.INTER_AREA)

                # 提取数字和序号
                name_body = file.split(".")[0]
                try:
                    if '-' in name_body:
                        prefix, rest = name_body.split('-')
                        number, index = rest.split('_')
                    else:
                        base, index = name_body.rsplit('_', 1)
                        number = base.split('_')[-1]  # 仅提取数字部分
                except ValueError:
                    print(f"跳过无法解析名称的文件：{file}")
                    continue

                # 构造输出路径（bus 和 ceus）
                bus_out_dir = os.path.join(output_root, rel_path, 'bus')
                ceus_out_dir = os.path.join(output_root, rel_path, 'ceus')
                os.makedirs(bus_out_dir, exist_ok=True)
                os.makedirs(ceus_out_dir, exist_ok=True)

                # 保存 bus 图像（左）
                left_name = f"sh-{number}_bus_{index}.png"
                left_path = os.path.join(bus_out_dir, left_name)
                cv2.imwrite(left_path, left_resized)

                # 保存 ceus 图像（中）
                middle_name = f"sh-{number}_ceus_{index}.png"
                middle_path = os.path.join(ceus_out_dir, middle_name)
                cv2.imwrite(middle_path, middle_resized)

                print(f"保存：{left_path}")
                print(f"保存：{middle_path}")

# 设置路径
input_folder = r"Z:\课题组共享-临时使用\zzy\VEUS-main\VEUS-main\VEUS-main\train"  # 原始图像目录
output_folder = r"D:\XJJ\zaoying\2_Image_Restoration_deraing_raindrop_noise1\dataset\train"

crop_and_save_images(input_folder, output_folder)
