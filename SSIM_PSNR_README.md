# SSIM和PSNR指标计算功能

## 功能说明

已为RDDM模型添加了自动计算SSIM（结构相似性指数）和PSNR（峰值信噪比）指标的功能，用于评估生成图像与目标图像的质量。

## 修改内容

### 1. 新增函数
- `calculate_ssim_psnr(generated_img, target_img)`: 计算两张图像之间的SSIM和PSNR指标

### 2. 修改的函数
- `sample()`: 在保存sample图像时自动计算并输出指标
- `test()`: 在测试时自动计算并输出指标

## 输出格式

### Sample模式输出示例
```
Sample-0 Metrics:
  Average SSIM: 0.8542
  Average PSNR: 28.45 dB
  Individual SSIM: ['0.8234', '0.8567', '0.8789', '0.8578']
  Individual PSNR: ['27.23', '28.67', '29.45', '28.45']
```

### Test模式输出示例
```
Test-image001.png Metrics:
  SSIM: 0.8542
  PSNR: 28.45 dB
```

## 指标说明

### SSIM (结构相似性指数)
- **范围**: 0-1，越接近1表示图像越相似
- **含义**: 衡量图像的亮度、对比度和结构相似性
- **优点**: 更符合人眼视觉感知

### PSNR (峰值信噪比)
- **范围**: 通常10-50 dB，数值越高表示图像质量越好
- **含义**: 衡量图像的像素级差异
- **参考值**:
  - > 30 dB: 高质量
  - 20-30 dB: 中等质量
  - < 20 dB: 低质量

## 适用条件

该功能仅在以下条件下激活：
- `condition_type = 2` (双输入条件模式)
- 有足够的输入图像（目标模态和源模态）

## 依赖要求

需要安装 `scikit-image` 库：
```bash
pip install scikit-image
```

## 使用方法

无需额外配置，运行训练或测试时会自动计算并输出指标：

```bash
# 训练时会在sample阶段输出指标
python train.py

# 测试时会为每张图像输出指标
python test.py
```

## 注意事项

1. 指标计算基于生成的最终图像与目标模态图像的比较
2. 对于医学图像，建议结合专业的医学图像质量评估标准
3. SSIM和PSNR是通用指标，可能需要根据具体应用场景调整评估标准
