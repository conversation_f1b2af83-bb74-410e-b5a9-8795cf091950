import os
import nibabel as nib
import matplotlib.pyplot as plt
import re


def convert_nii_to_png(input_dir, output_dir):
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 遍历输入目录及其所有子目录
    for root, dirs, files in os.walk(input_dir):
        for file in files:
            if file.endswith('seg.nii.gz'):
                # 加载NIfTI文件
                nii_path = os.path.join(root, file)
                img = nib.load(nii_path)
                data = img.get_fdata()

                # 从文件名提取病例ID（假设为5位数字）
                file_base = file.replace('.nii.gz', '')
                match = re.search(r'\d{5}', file_base)
                if not match:
                    print(f"跳过文件（未找到病例ID）: {file}")
                    continue
                case_id = match.group(

                # 遍历所有切片
                for slice_idx in range(data.shape[2]):
                    # 生成标准化文件名
                    png_filename = f"{case_id}_seg_{slice_idx}.png"
                    png_path = os.path.join(output_dir, png_filename)

                    # 提取并保存切片
                    slice_data = data[:, :, slice_idx]
                    plt.imsave(png_path, slice_data, cmap='gray')
                    print(f"已保存: {png_path}")


# 设置输入和输出路径
input_path = r"E:\Glioblastoma\val"
output_path = r"D:\XCJ\RDDM-main\RDDM-main\seg_png"

# 调用转换函数
convert_nii_to_png(input_path, output_path)
