import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from skimage.transform import rotate, AffineTransform, warp
import os

def add_motion_blur(image, alpha=0.5):
    """
    添加运动伪影到MRI图像中，通过复制图像并设置透明度、随机倾斜率和平面移动距离实现。

    参数：
    - image: 2D numpy数组，输入的MRI图像
    - alpha: float, 复制图像的透明度

    返回：
    - image_with_artifacts: 2D numpy数组，带有运动伪影的图像
    """
    # 随机生成倾斜角度和移动距离
    angle = np.random.uniform(-25, 25)
    shift_x = np.random.uniform(-25, 25)
    shift_y = np.random.uniform(-25, 25)

    # 复制图像并应用透明度
    duplicate = image.copy()
    duplicate = np.clip(duplicate * alpha, 0, 255)

    # 应用倾斜变换
    transformed_image = rotate(duplicate, angle, resize=False, mode='edge')

    # 应用平面移动
    transform = AffineTransform(translation=(shift_x, shift_y))
    shifted_image = warp(transformed_image, transform, mode='edge')

    # 将原图和变换后的图像叠加
    image_with_artifacts = np.clip(image + shifted_image, 0, 255)

    return image_with_artifacts


import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
from skimage.transform import rotate, AffineTransform, warp


def add_motion_blur(image, alpha=0.5):
    """
    添加运动伪影到MRI图像中，通过复制图像并设置透明度、随机倾斜率和平面移动距离实现。

    参数：
    - image: 2D numpy数组，输入的MRI图像
    - alpha: float, 复制图像的透明度

    返回：
    - image_with_artifacts: 2D numpy数组，带有运动伪影的图像
    """
    # 随机生成倾斜角度和移动距离
    angle = np.random.uniform(-25, 25)
    shift_x = np.random.uniform(-25, 25)
    shift_y = np.random.uniform(-25, 25)

    # 复制图像并应用透明度
    duplicate = image.copy()
    duplicate = np.clip(duplicate * alpha, 0, 255)

    # 应用倾斜变换
    transformed_image = rotate(duplicate, angle, resize=False, mode='edge')

    # 应用平面移动
    transform = AffineTransform(translation=(shift_x, shift_y))
    shifted_image = warp(transformed_image, transform, mode='edge')

    # 将原图和变换后的图像叠加
    image_with_artifacts = np.clip(image + shifted_image, 0, 255)

    return image_with_artifacts


def process_images(input_folder, output_folder, alpha=0.5):
    """
    遍历文件夹中的所有图像，应用变换并保存到指定文件夹。

    参数：
    - input_folder: str, 输入图像文件夹路径
    - output_folder: str, 输出图像文件夹路径
    - alpha: float, 复制图像的透明度
    """
    # 创建输出文件夹（如果不存在）
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    # 遍历输入文件夹中的所有图像文件
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tif', '.tiff')):
            input_path = os.path.join(input_folder, filename)
            output_path = os.path.join(output_folder, filename)

            # 读取图像并转换为灰度图
            image = Image.open(input_path).convert('L')
            image = np.array(image)

            # 添加运动伪影
            image_with_artifacts = add_motion_blur(image, alpha=alpha)

            # 保存变换后的图像
            plt.imsave(output_path, image_with_artifacts, cmap='gray')
            print(f"保存成功：{output_path}")


# 示例使用
input_folder = r"D:\XCJ\RDDM-main\RDDM-main\experiments\5_Image_translation_dog_to_cat_wo_input_imgsize64_batch64_pred_res_noise\dataset\MRI\t1\test"  # 输入图像文件夹路径
output_folder = r"D:\XCJ\RDDM-main\RDDM-main\experiments\5_Image_translation_dog_to_cat_wo_input_imgsize64_batch64_pred_res_noise\dataset\Artifact\t1\test\motion"  # 输出图像文件夹路径
process_images(input_folder, output_folder, alpha=0.5)
