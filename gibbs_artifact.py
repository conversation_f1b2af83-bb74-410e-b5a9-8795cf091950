import os
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image

def truncate_k_space(fft_image, keep_fraction):
    assert 0 < keep_fraction < 1, "keep_fraction should be between 0 and 1"
    rows, cols = fft_image.shape
    crow, ccol = rows // 2, cols // 2

    mask = np.zeros_like(fft_image)
    r_keep = int(crow * keep_fraction)
    c_keep = int(ccol * keep_fraction)
    mask[crow - r_keep:crow + r_keep, ccol - c_keep:ccol + c_keep] = 1

    truncated_fft = fft_image * mask
    return truncated_fft

def add_gibbs_artifact_to_image(image_np, keep_fraction=0.5):
    image_fft = np.fft.fft2(image_np)
    image_fft_shifted = np.fft.fftshift(image_fft)

    truncated_fft = truncate_k_space(image_fft_shifted, keep_fraction)

    truncated_fft_shifted = np.fft.ifftshift(truncated_fft)
    image_reconstructed = np.fft.ifft2(truncated_fft_shifted)
    image_reconstructed = np.abs(image_reconstructed)
    return image_reconstructed

def process_images_in_folder(input_folder, output_folder, keep_fraction=0.5):
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)

    for filename in os.listdir(input_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            image_path = os.path.join(input_folder, filename)
            image = Image.open(image_path).convert('L')
            image_np = np.array(image)

            image_with_artifact = add_gibbs_artifact_to_image(image_np, keep_fraction)

            output_image = Image.fromarray(image_with_artifact.astype(np.uint8))
            output_path = os.path.join(output_folder, filename)
            output_image.save(output_path)
            print(f"Processed and saved: {output_path}")

# 示例使用
input_folder = r"D:\XCJ\RDDM-main\RDDM-main\experiments\5_Image_translation_dog_to_cat_wo_input_imgsize64_batch64_pred_res_noise\dataset\MRI\t1\test"  # 输入图像文件夹路径
output_folder = r"D:\XCJ\RDDM-main\RDDM-main\experiments\5_Image_translation_dog_to_cat_wo_input_imgsize64_batch64_pred_res_noise\dataset\Artifact\t1\test\gibbs"  # 输出图像文件夹路径
keep_fraction = 0.5  # 调整保留的k-space数据比例

process_images_in_folder(input_folder, output_folder, keep_fraction)
