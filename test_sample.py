import os
import sys
import torch
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image
from torchvision import transforms
import torchvision.utils as vutils
from pathlib import Path
from tqdm import tqdm

# 从修改后的文件导入所有需要的组件
from src.residual_denoising_diffusion_KAN_loss import (ResidualDiffusion,
                                                   Trainer, Unet, UnetRes,
                                                   set_seed, normalize_to_neg_one_to_one,
                                                   unnormalize_to_zero_to_one,
                                                   GaussianDiffusion)

# init
os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(str(e) for e in [0])
sys.stdout.flush()
set_seed(10)
debug = False
if debug:
    save_and_sample_every = 2
    sampling_timesteps = 10
    sampling_timesteps_original_ddim_ddpm = 10
    train_num_steps = 200
else:
    save_and_sample_every = 1000
    if len(sys.argv) > 1:
        sampling_timesteps = int(sys.argv[1])
    else:
        sampling_timesteps = 10
    sampling_timesteps_original_ddim_ddpm = 250
    train_num_steps = 80000

original_ddim_ddpm = False
if original_ddim_ddpm:
    condition = False
    input_condition = False
    input_condition_mask = False
else:
    condition = True
    input_condition = False
    input_condition_mask = False
    use_tumor_mask = True  # 启用肿瘤掩码

if condition:
    if input_condition:
        folder = [
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_free_train.flist",
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_train.flist",
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_mask_train.flist",
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_free_test.flist",
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_test.flist",
            "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_mask_test.flist"]
    else:
        folder = ["/home/<USER>/data-set/program/diffusion/dataset/deraing_raindrop/raindrop_data/train_gt.flist",
                  "/home/<USER>/data-set/program/diffusion/dataset/deraing_raindrop/raindrop_data/train_input.flist",
                  "/home/<USER>/data-set/program/diffusion/dataset/deraing_raindrop/raindrop_data/test_gt.flist",
                  "/home/<USER>/data-set/program/diffusion/dataset/deraing_raindrop/raindrop_data/test_input.flist"]
    train_batch_size = 1
    num_samples = 1
    sum_scale = 1
    image_size = 256
else:
    folder = '/home/<USER>/disk12t/liu_data/dataset/CelebA/img_align_celeba'
    train_batch_size = 32
    num_samples = 25
    sum_scale = 1
    image_size = 32

if original_ddim_ddpm:
    model = Unet(
        dim=64,
        dim_mults=(1, 2, 4, 8)
    )
    diffusion = GaussianDiffusion(
        model,
        image_size=image_size,
        timesteps=1000,  # number of steps
        sampling_timesteps=sampling_timesteps_original_ddim_ddpm,
        loss_type='l1',  # L1 or L2
    )
else:
    model = UnetRes(
        dim=64,
        dim_mults=(1, 2, 4),
        share_encoder=0,
        condition=condition,
        input_condition=input_condition
    )
    diffusion = ResidualDiffusion(
        model,
        image_size=image_size,
        timesteps=1000,  # number of steps
        # number of sampling timesteps (using ddim for faster inference [see citation for ddim paper])
        sampling_timesteps=sampling_timesteps,
        objective='pred_res_noise',
        loss_type='l1',  # L1 or L2
        condition=condition,
        sum_scale=sum_scale,
        input_condition=input_condition,
        input_condition_mask=input_condition_mask,
        use_tumor_mask=use_tumor_mask,  # 启用肿瘤掩码
        tumor_loss_weight=10.0  # 设置肿瘤区域损失权重
    )

trainer = Trainer(
    diffusion,
    folder,
    train_batch_size=train_batch_size,
    num_samples=num_samples,
    train_lr=8e-5,
    train_num_steps=train_num_steps,  # total training steps
    gradient_accumulate_every=2,  # gradient accumulation steps
    ema_decay=0.995,  # exponential moving average decay
    amp=False,  # turn on mixed precision
    convert_image_to="RGB",
    results_folder='./results/sample_KAN_loss',
    condition=condition,
    save_and_sample_every=save_and_sample_every,
    equalizeHist=False,
    crop_patch=False,
    generation=False
)


# 添加用于可视化去噪过程的函数
def save_denoising_process(result_images, save_path, num_steps=5):
    """
    保存去噪过程的可视化结果

    参数:
        result_images: 包含去噪过程中间结果的列表
        save_path: 保存路径
        num_steps: 展示的步骤数量
    """
    # 确保保存目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    # 选择要显示的几个关键步骤
    step_indices = np.linspace(0, len(result_images) - 1, num_steps).astype(int)
    selected_images = [result_images[i] for i in step_indices]

    # 转换为PIL图像并排成一行
    pil_images = []
    for img in selected_images:
        if isinstance(img, torch.Tensor):
            # 确保是正确的值范围并转换为PIL
            img = unnormalize_to_zero_to_one(img)
            img = img.squeeze().permute(1, 2, 0).cpu().numpy().clip(0, 1)
            img = (img * 255).astype(np.uint8)
            pil_images.append(Image.fromarray(img))
        elif isinstance(img, np.ndarray):
            pil_images.append(Image.fromarray((img * 255).astype(np.uint8)))
        else:
            pil_images.append(img)

    # 计算拼接图像的尺寸
    width = sum(img.width for img in pil_images)
    height = max(img.height for img in pil_images)

    # 创建新图像
    result = Image.new('RGB', (width, height))

    # 将所有图像粘贴到新图像上
    x_offset = 0
    for img in pil_images:
        result.paste(img, (x_offset, 0))
        x_offset += img.width

    # 保存拼接后的图像
    result.save(save_path)


# 添加用于可视化网格形式去噪过程的函数
def visualize_denoising_grid(result_images, save_path, nrow=3, ncol=2, add_noise=True, add_gt=False, gt_img=None,
                             input_img=None):
    """
    保存类似于图片中所示的网格形式去噪过程可视化结果

    参数:
        result_images: 包含去噪过程中间结果的列表
        save_path: 保存路径
        nrow: 行数
        ncol: 列数
        add_noise: 是否在第一行添加高噪声图像
        add_gt: 是否在最后一格添加目标图像
        gt_img: 目标图像
        input_img: 输入图像
    """
    # 确保保存目录存在
    os.makedirs(os.path.dirname(save_path), exist_ok=True)

    # 计算要选择的图像间隔
    if add_noise:
        # 如果添加噪声，第一行都是噪声比较大的
        start_idx = 0
        end_idx = len(result_images) - 1
        steps = nrow * ncol - (1 if add_gt else 0)
        indices = np.linspace(start_idx, end_idx, steps).astype(int)
    else:
        # 均匀选取去噪过程中的图像
        steps = nrow * ncol - (1 if add_gt else 0)
        indices = np.linspace(0, len(result_images) - 1, steps).astype(int)

    # 选择图像
    selected_images = [result_images[i] for i in indices]

    # 转换为Tensor
    tensors = []
    for img in selected_images:
        if isinstance(img, torch.Tensor):
            # 确保是正确的值范围
            img = unnormalize_to_zero_to_one(img)
            tensors.append(img.squeeze())
        else:
            # 如果不是tensor，转换为tensor
            transform = transforms.ToTensor()
            tensors.append(transform(img))

    # 如果需要添加目标图像
    if add_gt and gt_img is not None:
        if isinstance(gt_img, torch.Tensor):
            gt_tensor = unnormalize_to_zero_to_one(gt_img)
            tensors.append(gt_tensor.squeeze())
        else:
            transform = transforms.ToTensor()
            tensors.append(transform(gt_img))

    # 创建网格图像
    grid = vutils.make_grid(tensors, nrow=ncol, padding=2, normalize=False)

    # 保存网格图像
    vutils.save_image(grid, save_path, normalize=False)

    return grid


# 修改测试函数以支持输出去噪过程
def test_with_denoising_visualization(trainer, save_folder, checkpoint, show_process=True):
    """进行测试并可视化去噪过程"""
    # 加载检查点
    if not trainer.accelerator.is_local_main_process:
        return

    trainer.load(checkpoint)
    trainer.set_results_folder(save_folder)

    # 获取数据加载器
    dataloader = trainer.test_dataloader
    device = trainer.accelerator.device

    # 创建保存目录
    process_dir = os.path.join(save_folder, 'denoising_process')
    os.makedirs(process_dir, exist_ok=True)

    # 测试每个样本
    for i, data in enumerate(dataloader):
        # 获取输入和目标
        if trainer.model.condition:
            gt, condition = data
            gt, condition = gt.to(device), condition.to(device)
            # 在这里可以添加肿瘤掩码，如果有的话

            # 创建测试输入
            test_input = [gt, condition]

            # 获取去噪过程（而不是最终结果）
            with torch.no_grad():
                # 修改为获取完整去噪过程
                shape = gt.shape
                # 使用 p_sample_loop 获取所有中间结果，设置 last=False
                result_images = trainer.model.p_sample_loop(test_input, shape, last=False)

                # 保存原始图像、条件和最终结果
                input_path = os.path.join(save_folder, f'test_{i}_input.png')
                target_path = os.path.join(save_folder, f'test_{i}_target.png')
                result_path = os.path.join(save_folder, f'test_{i}_result.png')

                # 保存输入条件图像
                condition_img = unnormalize_to_zero_to_one(condition)
                vutils.save_image(condition_img, input_path)

                # 保存目标图像
                target_img = unnormalize_to_zero_to_one(gt)
                vutils.save_image(target_img, target_path)

                # 保存最终结果
                final_img = unnormalize_to_zero_to_one(result_images[-1])
                vutils.save_image(final_img, result_path)

                # 如果需要显示去噪过程
                if show_process:
                    # 水平排列的去噪过程
                    process_path = os.path.join(process_dir, f'process_{i}_horizontal.png')
                    save_denoising_process(result_images, process_path, num_steps=6)

                    # 网格形式的去噪过程 - 类似于图片中所示的6张图的网格布局
                    grid_path = os.path.join(process_dir, f'process_{i}_grid.png')
                    visualize_denoising_grid(
                        result_images,
                        grid_path,
                        nrow=2,  # 2行
                        ncol=3,  # 3列
                        add_noise=True,
                        add_gt=True,
                        gt_img=gt,
                        input_img=condition
                    )
        else:
            # 无条件生成的情况
            # 使用与条件生成类似的方法处理
            pass

        # 限制测试样本数量
        if i >= 10:  # 可以根据需要调整
            break

    print(f"测试完成，结果保存在 {save_folder}")


# 执行测试
if __name__ == "__main__":
    # 设置检查点和结果保存目录
    checkpoint = 70
    save_folder = f'./results/test_with_process_{checkpoint}pt_steps{sampling_timesteps}'

    # 执行测试和可视化
    test_with_denoising_visualization(trainer, save_folder, checkpoint, show_process=True)