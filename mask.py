import cv2
import numpy as np
import os

def threshold_segmentation(input_folder, output_folder, threshold_value=50):
    # 确保输出文件夹存在
    os.makedirs(output_folder, exist_ok=True)

    # 遍历输入文件夹中的所有图像文件
    for filename in os.listdir(input_folder):
        if filename.endswith(('.png', '.jpg', '.jpeg')):  # 只处理图像文件
            input_image_path = os.path.join(input_folder, filename)
            output_image_path = os.path.join(output_folder, filename)

            # 读取分割标签图像（灰度图）
            segmentation_map = cv2.imread(input_image_path, cv2.IMREAD_GRAYSCALE)

            if segmentation_map is None:
                print(f"无法读取图像: {input_image_path}，请检查路径。")
                continue

            # 二值化处理
            _, binary_mask = cv2.threshold(segmentation_map, threshold_value, 1, cv2.THRESH_BINARY)

            # 将二值化掩码转换为 0 和 255 的格式以便保存
            binary_mask = binary_mask * 255

            # 保存二值化掩码
            cv2.imwrite(output_image_path, binary_mask)

            print(f"二值化掩码已保存到: {output_image_path}")

# 使用示例
input_folder = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset\train\seg_1'  # 输入文件夹路径
output_folder = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset\train\seg'  # 输出文件夹路径
threshold_segmentation(input_folder, output_folder)