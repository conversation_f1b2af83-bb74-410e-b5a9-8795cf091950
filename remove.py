import os
import shutil
import re


def accurate_copy():
    seg_dir = r'D:\XCJ\RDDM-main\RDDM-main\train\seg'
    t1ce_dir = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset\test\t1ce'
    output_dir = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset\test\seg'

    # 路径验证
    print("[路径验证]")
    print(f"Seg目录存在: {os.path.exists(seg_dir)}")
    print(f"T1CE目录存在: {os.path.exists(t1ce_dir)}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录准备就绪: {os.path.exists(output_dir)}\n")

    # 构建t1ce索引（保持原始slice格式）
    t1ce_index = set()
    print("[解析t1ce文件]")
    for filename in os.listdir(t1ce_dir):
        parts = filename.split('_')
        if len(parts) == 3 and parts[1] == 't1ce':
            case_id = parts[0].zfill(5)  # 补零到5位
            slice_id = parts[2].split('.')[0]  # 去除扩展名
            t1ce_index.add((case_id, slice_id))
            print(f"注册条件: case={case_id}, slice={slice_id}")

    print(f"\n共发现{len(t1ce_index)}组有效条件\n")

    # 处理seg文件
    copied_count = 0
    print("[扫描seg_png文件]")
    for filename in os.listdir(seg_dir):
        if not filename.endswith('.png'):
            continue

        # 精准分割文件名
        try:
            # 示例文件名：01666_seg_90.png
            base = filename[:-4]  # 去除.png
            parts = base.split('_')
            if len(parts) != 3 or parts[1] != 'seg':
                continue

            seg_case = parts[0].zfill(5)  # 补零到5位
            seg_slice = parts[2]  # 直接使用原始slice

            print(f"处理文件: {filename}")
            print(f"提取标识: case={seg_case}, slice={seg_slice}")

            if (seg_case, seg_slice) in t1ce_index:
                src = os.path.join(seg_dir, filename)
                dest = os.path.join(output_dir, filename)
                shutil.copy2(src, dest)
                copied_count += 1
                print("√ 匹配成功")
            else:
                print(f"× 无对应记录（t1ce中未找到case={seg_case} slice={seg_slice}）")

        except Exception as e:
            print(f"! 处理异常: {str(e)}")

    print(f"\n操作完成，共复制{copied_count}个文件")


if __name__ == '__main__':
    accurate_copy()