import os
import numpy as np
import pandas as pd
from skimage.metrics import peak_signal_noise_ratio as psnr, structural_similarity as ssim
from skimage import io
from skimage.transform import resize

def min_max_normalize(values, min_val, max_val):
    """对给定的值进行Min-Max归一化，使用全局的最小和最大值"""
    return (values - min_val) / (max_val - min_val) if max_val > min_val else values

def ensure_three_channels(image):
    """确保图像为3通道，如果不是则进行处理"""
    if image.ndim == 2:  # 灰度图像
        return np.stack((image,)*3, axis=-1)  # 将灰度图像复制为3通道
    elif image.shape[2] == 4:  # 如果是RGBA图像
        return image[:, :, :3]  # 只保留RGB通道
    return image  # 如果已经是3通道，直接返回

def calculate_metrics(real_dir, fake_dir, output_excel):
    # Initialize lists to store results
    results = []

    # Get all image files in the real and fake directories
    real_images = os.listdir(real_dir)
    fake_images = os.listdir(fake_dir)

    # Create a set for quick lookup of fake images
    fake_image_set = set(fake_images)

    # Store PSNR and SSIM values to calculate global min/max later
    psnr_values = []
    ssim_values = []

    for real_image in real_images:
        # Extract case ID and slice ID from the filename
        parts = os.path.splitext(real_image)[0].split('_')
        case_id = parts[0]
        slice_id = parts[-1]

        # Construct the corresponding fake image filename
        fake_image_name = f"{case_id}_t2_{slice_id}.png"

        # Check if the fake image exists
        if fake_image_name in fake_image_set:
            # Load the images
            real_image_path = os.path.join(real_dir, real_image)
            fake_image_path = os.path.join(fake_dir, fake_image_name)

            real_img = io.imread(real_image_path).astype(np.float32) / 255.0  # Normalize to [0, 1]
            fake_img = io.imread(fake_image_path).astype(np.float32) / 255.0  # Normalize to [0, 1]

            # Resize images to 256x256
            real_img = resize(real_img, (256, 256), anti_aliasing=True)
            fake_img = resize(fake_img, (256, 256), anti_aliasing=True)

            # Ensure images are 3 channels
            real_img = ensure_three_channels(real_img)
            fake_img = ensure_three_channels(fake_img)

            # Calculate PSNR and SSIM
            psnr_value = psnr(real_img, fake_img, data_range=1)
            ssim_value = ssim(real_img, fake_img,
                              data_range=1, multichannel=True, win_size=7, channel_axis=-1)

            # Append values to lists for later normalization
            psnr_values.append(psnr_value)
            ssim_values.append(ssim_value)

            # Append results for this image
            results.append({
                'Image': fake_image_name,
                'PSNR': psnr_value,
                'SSIM': ssim_value
            })

            print(f"Processed {fake_image_name}: PSNR = {psnr_value:.3f}, SSIM = {ssim_value:.3f}")
        else:
            print(f"Warning: Fake image {fake_image_name} not found for real image {real_image}")

    # Check if results are empty
    if not results:
        print("No matching images found. Exiting.")
        return

    # Convert to DataFrame for easier analysis
    results_df = pd.DataFrame(results)

    # Calculate global min and max for PSNR and SSIM
    min_psnr, max_psnr = np.min(psnr_values), np.max(psnr_values)
    min_ssim, max_ssim = np.min(ssim_values), np.max(ssim_values)

    # Apply global Min-Max normalization to PSNR and SSIM
    results_df['Normalized PSNR'] = min_max_normalize(results_df['PSNR'].values, min_psnr, max_psnr)
    results_df['Normalized SSIM'] = min_max_normalize(results_df['SSIM'].values, min_ssim, max_ssim)

    # Calculate combined metric (CM)
    results_df['CM'] = (results_df['Normalized PSNR'] + results_df['Normalized SSIM']) / 2

    # Print the DataFrame to check its contents
    print(results_df)

    # Calculate mean and std for PSNR, SSIM, and CM
    mean_psnr = results_df['PSNR'].mean()
    std_psnr = results_df['PSNR'].std()
    mean_ssim = results_df['SSIM'].mean()
    std_ssim = results_df['SSIM'].std()
    mean_cm = results_df['CM'].mean()
    std_cm = results_df['CM'].std()

    # Save results to an Excel file
    summary_data = {
        'Mean PSNR': [mean_psnr],
        'Std PSNR': [std_psnr],
        'Mean SSIM': [mean_ssim],
        'Std SSIM': [std_ssim],
        'Mean CM': [mean_cm],
        'Std CM': [std_cm]
    }
    summary_df = pd.DataFrame(summary_data)

    # Save the individual image metrics and the summary metrics to Excel
    with pd.ExcelWriter(output_excel) as writer:
        results_df.to_excel(writer, sheet_name='Image Metrics', index=False)
        summary_df.to_excel(writer, sheet_name='Summary', index=False)

    print(f"Results saved to {output_excel}")

if __name__ == '__main__':
    real_images_directory = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\dataset\test\t1'  # Replace with the path to the real images
    fake_images_directory = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\results\Mask_t2_t1_51_10'  # Replace with the path to the generated images
    output_file = r'D:\XCJ\RDDM-main\RDDM-main\experiments\2_Image_Restoration_deraing_raindrop_noise1\results\Mask_t2_t1_51_10\image_metrics_summary.xlsx'  # Output Excel file

    calculate_metrics(real_images_directory, fake_images_directory, output_file)
