import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
import torch
from src.denoising_diffusion_pytorch import GaussianDiffusion
from residual_denoising_diffusion_Mask import (ResidualDiffusion,
                                                   Trainer, Unet, UnetRes,
                                                   set_seed)

from torchinfo import summary

# init 
os.environ['CUDA_VISIBLE_DEVICES'] = ','.join(str(e) for e in [0])
sys.stdout.flush()
set_seed(10)
debug = False
if debug:
    save_and_sample_every = 2
    sampling_timesteps = 10
    sampling_timesteps_original_ddim_ddpm = 10
    train_num_steps = 200
else:
    save_and_sample_every = 200
    if len(sys.argv)>1:
        sampling_timesteps = int(sys.argv[1])
    else:
        sampling_timesteps = 10
    sampling_timesteps_original_ddim_ddpm = 250
    train_num_steps = 12000

original_ddim_ddpm = False
if original_ddim_ddpm:
    condition = False
    input_condition = False
    input_condition_mask = False
else:
    condition = True
    input_condition = False
    input_condition_mask = False  # 确保这个设为 False，因为没有分割标签

if condition:
    if input_condition:
        folder = ["/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_free_train.flist",
                "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_train.flist",
                "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_mask_train.flist",
                "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_free_test.flist",
                "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_shadow_test.flist",
                "/home/<USER>/disk12t/liu_data/shadow_removal_with_val_dataset/ISTD_Dataset_arg/data_val/ISTD_mask_test.flist"]
    else:
        folder = [r"E:\learning\zaoying\RDDM-main\2_Image_Restoration_deraing_raindrop_noise1\dataset\train\ceus",
                  r"E:\learning\zaoying\RDDM-main\2_Image_Restoration_deraing_raindrop_noise1\dataset\train\bus",
                  r"E:\learning\zaoying\RDDM-main\2_Image_Restoration_deraing_raindrop_noise1\dataset\test\ceus",
                  r"E:\learning\zaoying\RDDM-main\2_Image_Restoration_deraing_raindrop_noise1\dataset\test\bus"]
    train_batch_size = 1
    num_samples = 4
    sum_scale = 1
    image_size = 256
else:
    folder = '/home/<USER>/disk12t/liu_data/dataset/CelebA/img_align_celeba'
    train_batch_size = 32
    num_samples = 25
    sum_scale = 1
    image_size = 32

if original_ddim_ddpm:
    model = Unet(
        dim = 64,
        dim_mults = (1, 2, 4, 8)
    )
    diffusion = GaussianDiffusion(
        model,
        image_size=image_size,
        timesteps=1000,           # number of steps
        sampling_timesteps=sampling_timesteps_original_ddim_ddpm,
        loss_type='l1',            # L1 or L2
    )
else:
    model = UnetRes(
        dim=64,
        dim_mults=(1, 2, 4, 8, 16),
        share_encoder=0,
        condition=condition,
        input_condition=input_condition
    )


    # x_dummy = torch.randn(1, 6, image_size, image_size)
    # time_dummy = torch.tensor([500], dtype=torch.long)  # 时间步示例
    #
    # # 关键：将输入参数打包成元组
    # input_data = (x_dummy, time_dummy)
    # print("self.unet0网络结构:")
    # # 使用input_data代替input_size
    # summary(
    #     model.unet0,
    #     input_data=input_data,  # 直接传递输入数据
    #     depth=5,  # 展开层级深度
    #     col_names=["input_size", "output_size", "num_params"]
    # )
    # print("self.unet1网络结构:")
    # # 使用input_data代替input_size
    # summary(
    #     model.unet1,
    #     input_data=input_data,  # 直接传递输入数据
    #     depth=5,  # 展开层级深度
    #     col_names=["input_size", "output_size", "num_params"]
    # )

    diffusion = ResidualDiffusion(
        model,
        image_size=image_size,
        timesteps=1000,           # number of steps
        # number of sampling timesteps (using ddim for faster inference [see citation for ddim paper])
        sampling_timesteps=sampling_timesteps,
        objective='pred_res_noise',
        loss_type='l1',            # L1 or L2
        condition=condition,
        sum_scale = sum_scale,
        input_condition=input_condition,
        input_condition_mask=input_condition_mask
    )

trainer = Trainer(
    diffusion,
    folder,
    train_batch_size=train_batch_size,
    num_samples=num_samples,
    train_lr=8e-5,
    train_num_steps=train_num_steps,         # total training steps
    gradient_accumulate_every=1,    # gradient accumulation steps
    ema_decay=0.995,                # exponential moving average decay
    amp=False,                        # turn on mixed precision
    convert_image_to="RGB",
    results_folder=r'E:/learning/zaoying/RDDM-main/2_Image_Restoration_deraing_raindrop_noise1/results/sample_KAN_Mask_t2_motion',
    condition=condition,
    save_and_sample_every=save_and_sample_every,
    equalizeHist=False,
    crop_patch=False,
    generation = False
)

if __name__ == "__main__":

    model_checkpoint = 12 # 你可以改成最新的 checkpoint
    model_path = f"E:/learning/zaoying/RDDM-main/2_Image_Restoration_deraing_raindrop_noise1/results/sample_KAN_Mask_t2_motion/model-{model_checkpoint}.pt"

    if os.path.exists(model_path):
        print(f"Loading checkpoint from {model_path}")
        trainer.load_compatible(model_checkpoint)
    else:
        print("No previous checkpoint found. Training from scratch.")

    # if not trainer.accelerator.is_local_main_process:
    #      pass
    # else:
    #      trainer.load(80)

    # train
    trainer.train()

    # test
    if not trainer.accelerator.is_local_main_process:
        pass
    else:
        trainer.load(trainer.train_num_steps//save_and_sample_every)
        trainer.set_results_folder('E:/learning/zaoying/RDDM-main/2_Image_Restoration_deraing_raindrop_noise1/results/test_timestep_'+str(sampling_timesteps))
        trainer.test(last=True)

    # trainer.set_results_folder('./results/test_sample')
    # trainer.test(sample=True)

def shift(dim):
    """Shift operation for KAN blocks"""
    x_shift = [ torch.roll(x_c, shift, dim=2) for x_c, shift in zip(torch.chunk(dim,2,dim=1), [-1, 1])]
    y_shift = [ torch.roll(y_c, shift, dim=3) for y_c, shift in zip(torch.chunk(torch.cat(x_shift,dim=1),2,dim=1), [-1, 1])]
    return torch.cat(y_shift,dim=1)

